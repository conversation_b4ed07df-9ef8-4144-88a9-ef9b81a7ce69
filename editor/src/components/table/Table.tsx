import React from "react";
import type { TableCell, TableProperties } from "@/types/table";
import type { TestDataRecord } from "@/utils/apiService";
import { processContentWithVariables } from "@/utils/contentProcessing";
import {
	calculateScaledDimensions,
	getResolvedCellBorders,
} from "@/utils/tableUtils";

interface TableProps {
	tableProperties: TableProperties;
	onResize?: (width: number, height: number) => void;
	containerWidthMm?: number;
	containerHeightMm?: number;
	documentVariables?: string[];
	testData?: TestDataRecord[];
	selectedTestDataIndex?: number;
	highlightVariables?: boolean;
}

// Wrap the component definition with React.memo
export const Table = React.memo(
	({
		tableProperties,
		containerWidthMm,
		containerHeightMm,
		testData = [],
		selectedTestDataIndex = -1,
		highlightVariables = false,
	}: TableProps) => {
		// Use shared helper to derive scaled dimensions and visibility flag
		const {
			scaledWidths: scaledColumnWidths,
			scaledHeights: scaledRowHeights,
			hasValidDimensions,
		} = calculateScaledDimensions(
			tableProperties.columnWidths,
			tableProperties.rowHeights,
			containerWidthMm,
			containerHeightMm,
		);

		// Define the cell rendering function for view mode
		const renderViewCell = (
			cell: TableCell,
			rowIndex: number,
			colIndex: number,
		): React.ReactNode => {
			// Use resolved borders to handle conflicts between adjacent cells
			const globalBorderColor = tableProperties.borderColor || "black";
			const resolvedBorders = getResolvedCellBorders(
				tableProperties.cells,
				rowIndex,
				colIndex,
				globalBorderColor,
			);

			const cellBorderTop = resolvedBorders.top.width;
			const cellBorderRight = resolvedBorders.right.width;
			const cellBorderBottom = resolvedBorders.bottom.width;
			const cellBorderLeft = resolvedBorders.left.width;

			const topColor = resolvedBorders.top.color;
			const rightColor = resolvedBorders.right.color;
			const bottomColor = resolvedBorders.bottom.color;
			const leftColor = resolvedBorders.left.color;

			const borderStyle = resolvedBorders.top.style; // All borders use same style

			// Helper: calculate logical column index (taking previous colspans into account)
			const rowCells = tableProperties.cells[rowIndex];
			const logicalColStart = rowCells
				.slice(0, colIndex)
				.reduce((sum, c) => sum + c.colspan, 0);
			const colWidthsMmSource =
				scaledColumnWidths.length > 0
					? scaledColumnWidths
					: tableProperties.columnWidths || [];

			// Sum the widths of all columns this cell spans
			const spannedWidthMm = colWidthsMmSource
				.slice(logicalColStart, logicalColStart + cell.colspan)
				.reduce((sum, w) => sum + (w || 0), 0);
			const cellWidthStyle =
				spannedWidthMm > 0 ? `${spannedWidthMm}mm` : undefined;

			// Calculate total height for cells spanning multiple rows
			const rowHeightsSource =
				scaledRowHeights.length > 0
					? scaledRowHeights
					: tableProperties.rowHeights || [];
			const spannedHeightMm = rowHeightsSource
				.slice(rowIndex, rowIndex + cell.rowspan)
				.reduce((sum, h) => sum + (h || 0), 0);
			const cellHeightStyle =
				spannedHeightMm > 0 ? `${spannedHeightMm}mm` : undefined;

			return (
				<td
					key={`${rowIndex}-${colIndex}`}
					colSpan={cell.colspan}
					rowSpan={cell.rowspan}
					style={{
						// Individual borders
						borderTop: `${cellBorderTop}px ${borderStyle} ${topColor}`,
						borderRight: `${cellBorderRight}px ${borderStyle} ${rightColor}`,
						borderBottom: `${cellBorderBottom}px ${borderStyle} ${bottomColor}`,
						borderLeft: `${cellBorderLeft}px ${borderStyle} ${leftColor}`,
						padding: `2px`,
						backgroundColor: cell.backgroundColor || "transparent",
						position: "relative",
						minWidth: "5px",
						width: cellWidthStyle || `15mm`,
						height: cellHeightStyle || `10mm`,
						maxHeight: cellHeightStyle || `10mm`,
						boxSizing: "border-box",
						wordBreak: "break-word",
						overflowWrap: "break-word",
						whiteSpace: "pre-wrap",
						verticalAlign: cell.verticalAlign || "top",
						overflow: "hidden",
					}}
				>
					<div
						className="max-w-none w-full"
						style={{
							height: "100%",
							maxHeight: "100%",
							overflow: "hidden",
							fontFamily: "'NeoSansforeprimo-Regular', sans-serif",
							whiteSpace: "pre-wrap",
							wordBreak: "break-word",
							overflowWrap: "break-word",
							textOverflow: "ellipsis",
							lineHeight: "1.2",
							display: "flex",
							flexDirection: "column",
							justifyContent:
								cell.verticalAlign === "middle"
									? "center"
									: cell.verticalAlign === "bottom"
										? "flex-end"
										: "flex-start",
						}}
						data-element-content
						// biome-ignore lint/security/noDangerouslySetInnerHtml: is needed
						dangerouslySetInnerHTML={{
							__html: processContentWithVariables({
								content: cell.content,
								isEditing: false,
								highlightVariables,
								selectedTestDataIndex,
								testData,
							}),
						}}
					/>
				</td>
			);
		};

		return (
			<div
				className="overflow-visible w-full h-full"
				style={{
					boxSizing: "border-box",
					visibility: hasValidDimensions ? "visible" : "hidden",
				}}
			>
				<table
					className="border-collapse table-fixed"
					style={{
						width: "100%",
						height: "100%",
						backgroundColor: tableProperties.backgroundColor || "transparent",
						borderSpacing: 0, // Ensure no spacing between cells
					}}
				>
					<tbody>
						{/* Use the TableBodyContent component instead of renderTableBodyContent function */}
						<TableBodyContent
							tableProperties={tableProperties}
							rowHeightsMm={
								scaledRowHeights.length > 0
									? scaledRowHeights
									: tableProperties.rowHeights || []
							}
							columnWidthsMm={
								scaledColumnWidths.length > 0
									? scaledColumnWidths
									: tableProperties.columnWidths || []
							}
							renderCell={renderViewCell}
						/>
					</tbody>
				</table>
			</div>
		);
	},
);

// Optionally, add a display name for better debugging
Table.displayName = "Table";

interface TableBodyContentProps {
	tableProperties: TableProperties;
	// Expect dimensions in mm
	columnWidthsMm: number[];
	rowHeightsMm: number[];
	// Function provided by the parent to render the actual <td> element
	renderCell: (
		cell: TableCell,
		rowIndex: number,
		colIndex: number,
	) => React.ReactNode;
}

/**
 * Renders the content of a <tbody> element based on table properties.
 * It iterates through rows and calls the provided renderCell function for each cell.
 */
export const TableBodyContent: React.FC<TableBodyContentProps> = ({
	tableProperties,
	rowHeightsMm,
	renderCell,
}) => {
	return (
		<>
			{tableProperties.cells.map((row, rowIndex) => (
				<tr
					key={`row-${rowIndex}-${row.length}`}
					style={{
						// Use intrinsic height in mm, convert to px for style
						height: rowHeightsMm?.[rowIndex]
							? `${rowHeightsMm[rowIndex]}mm`
							: `10mm`, // Default height (e.g., 10mm)
						maxHeight: rowHeightsMm?.[rowIndex]
							? `${rowHeightsMm[rowIndex]}mm`
							: `10mm`,
						boxSizing: "border-box",
						overflow: "hidden",
					}}
				>
					{row.map((cell, colIndex) => {
						// Delegate rendering the entire <td> to the parent component
						return renderCell(cell, rowIndex, colIndex);
					})}
				</tr>
			))}
		</>
	);
};
