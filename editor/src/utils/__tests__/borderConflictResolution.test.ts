import { describe, expect, it } from "vitest";
import type { TableCell, TableCellBorder } from "@/types/table";
import { resolveBorderConflict, getResolvedCellBorders } from "../tableUtils";

describe("Border Conflict Resolution", () => {
	const createTestCell = (
		colspan = 1,
		rowspan = 1,
		borderSettings?: Partial<{
			top: TableCellBorder;
			right: TableCellBorder;
			bottom: TableCellBorder;
			left: TableCellBorder;
		}>,
	): TableCell => ({
		content: "Test",
		colspan,
		rowspan,
		backgroundColor: null,
		borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
		borderSettings,
	});

	describe("resolveBorderConflict", () => {
		it("should use default border when neither cell has border settings", () => {
			const result = resolveBorderConflict(undefined, undefined, "#000000", 1);
			expect(result).toEqual({
				width: 1,
				color: "#000000",
				style: "solid",
			});
		});

		it("should use border from first cell when only first cell has border", () => {
			const border1: TableCellBorder = { width: 3, color: "#ff0000" };
			const result = resolveBorderConflict(border1, undefined, "#000000", 1);
			expect(result).toEqual({
				width: 3,
				color: "#ff0000",
				style: "solid",
			});
		});

		it("should use border from second cell when only second cell has border", () => {
			const border2: TableCellBorder = { width: 2, color: "#00ff00" };
			const result = resolveBorderConflict(undefined, border2, "#000000", 1);
			expect(result).toEqual({
				width: 2,
				color: "#00ff00",
				style: "solid",
			});
		});

		it("should prefer thicker border when both cells have borders", () => {
			const border1: TableCellBorder = { width: 2, color: "#ff0000" };
			const border2: TableCellBorder = { width: 4, color: "#00ff00" };
			const result = resolveBorderConflict(border1, border2, "#000000", 1);
			expect(result).toEqual({
				width: 4,
				color: "#00ff00",
				style: "solid",
			});
		});

		it("should prefer first border when both have same width", () => {
			const border1: TableCellBorder = { width: 3, color: "#ff0000" };
			const border2: TableCellBorder = { width: 3, color: "#00ff00" };
			const result = resolveBorderConflict(border1, border2, "#000000", 1);
			expect(result).toEqual({
				width: 3,
				color: "#ff0000",
				style: "solid",
			});
		});

		it("should handle null colors by using global color", () => {
			const border1: TableCellBorder = { width: 2, color: null };
			const border2: TableCellBorder = { width: 1, color: "#00ff00" };
			const result = resolveBorderConflict(border1, border2, "#0000ff", 1);
			expect(result).toEqual({
				width: 2,
				color: "#0000ff",
				style: "solid",
			});
		});
	});

	describe("getResolvedCellBorders", () => {
		it("should resolve borders for a simple 2x2 table", () => {
			const cells: TableCell[][] = [
				[
					createTestCell(1, 1, {
						right: { width: 3, color: "#ff0000" },
						bottom: { width: 2, color: "#ff0000" },
					}),
					createTestCell(1, 1, {
						left: { width: 1, color: "#00ff00" },
						bottom: { width: 4, color: "#00ff00" },
					}),
				],
				[
					createTestCell(1, 1, {
						top: { width: 1, color: "#0000ff" },
						right: { width: 2, color: "#0000ff" },
					}),
					createTestCell(1, 1, {
						top: { width: 3, color: "#ffff00" },
						left: { width: 1, color: "#ffff00" },
					}),
				],
			];

			// Test top-left cell (0,0)
			const topLeftBorders = getResolvedCellBorders(cells, 0, 0, "#000000");

			// Right border should prefer the thicker border (3px from left cell)
			expect(topLeftBorders.right.width).toBe(3);
			expect(topLeftBorders.right.color).toBe("#ff0000");

			// Bottom border should prefer the thicker border (2px from this cell vs 1px from bottom cell)
			expect(topLeftBorders.bottom.width).toBe(2);
			expect(topLeftBorders.bottom.color).toBe("#ff0000");

			// Test top-right cell (0,1)
			const topRightBorders = getResolvedCellBorders(cells, 0, 1, "#000000");

			// Left border should prefer the thicker border (3px from left cell)
			expect(topRightBorders.left.width).toBe(3);
			expect(topRightBorders.left.color).toBe("#ff0000");

			// Bottom border should prefer the thicker border (4px from this cell)
			expect(topRightBorders.bottom.width).toBe(4);
			expect(topRightBorders.bottom.color).toBe("#00ff00");
		});

		it("should handle cells with colspan and rowspan", () => {
			const cells: TableCell[][] = [
				[
					createTestCell(2, 1, {
						right: { width: 5, color: "#ff0000" },
						bottom: { width: 3, color: "#ff0000" },
					}), // Spans 2 columns
					createTestCell(1, 1, {
						left: { width: 2, color: "#00ff00" },
					}),
				],
				[
					createTestCell(1, 1, {
						top: { width: 1, color: "#0000ff" },
					}),
					createTestCell(1, 1, {
						top: { width: 2, color: "#ffff00" },
					}),
					createTestCell(1, 1, {
						left: { width: 3, color: "#ff00ff" },
					}),
				],
			];

			// Test the colspan cell (0,0)
			const colspanBorders = getResolvedCellBorders(cells, 0, 0, "#000000");

			// Right border should prefer the thicker border (5px from this cell)
			expect(colspanBorders.right.width).toBe(5);
			expect(colspanBorders.right.color).toBe("#ff0000");

			// Bottom border should prefer the thicker border (3px from this cell)
			expect(colspanBorders.bottom.width).toBe(3);
			expect(colspanBorders.bottom.color).toBe("#ff0000");
		});

		it("should handle edge cells correctly", () => {
			const cells: TableCell[][] = [
				[
					createTestCell(1, 1, {
						top: { width: 2, color: "#ff0000" },
						left: { width: 3, color: "#ff0000" },
					}),
				],
			];

			const borders = getResolvedCellBorders(cells, 0, 0, "#000000");

			// Edge borders should use the cell's own settings
			expect(borders.top.width).toBe(2);
			expect(borders.top.color).toBe("#ff0000");
			expect(borders.left.width).toBe(3);
			expect(borders.left.color).toBe("#ff0000");

			// Borders without neighbors should use default values
			expect(borders.right.width).toBe(1); // Default from borderWidths
			expect(borders.bottom.width).toBe(1); // Default from borderWidths
		});

		it("should fallback to borderWidths when no borderSettings exist", () => {
			const cells: TableCell[][] = [
				[
					{
						content: "Test",
						colspan: 1,
						rowspan: 1,
						backgroundColor: null,
						borderWidths: { top: 2, right: 3, bottom: 4, left: 5 },
						// No borderSettings
					},
				],
			];

			const borders = getResolvedCellBorders(cells, 0, 0, "#123456");

			// Should use borderWidths values with global color
			expect(borders.top.width).toBe(2);
			expect(borders.top.color).toBe("#123456");
			expect(borders.right.width).toBe(3);
			expect(borders.right.color).toBe("#123456");
			expect(borders.bottom.width).toBe(4);
			expect(borders.bottom.color).toBe("#123456");
			expect(borders.left.width).toBe(5);
			expect(borders.left.color).toBe("#123456");
		});
	});
});
